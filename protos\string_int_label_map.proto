// Message to store the mapping from class label strings to class id. Datasets
// use string labels to represent classes while the object detection framework
// works with class ids. This message maps them so they can be converted back
// and forth as needed.
syntax = "proto2";

package object_detection.protos;

message StringIntLabelMapItem {
  // String name. The most common practice is to set this to a MID or synsets
  // id.
  optional string name = 1;

  // Integer id that maps to the string name above. Label ids should start from
  // 1.
  optional int32 id = 2;

  // Human readable string label.
  optional string display_name = 3;
};

message StringIntLabelMap {
  repeated StringIntLabelMapItem item = 1;
};
