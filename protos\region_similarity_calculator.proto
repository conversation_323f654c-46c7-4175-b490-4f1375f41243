syntax = "proto2";

package object_detection.protos;

// Configuration proto for region similarity calculators. See
// core/region_similarity_calculator.py for details.
message RegionSimilarityCalculator {
  oneof region_similarity {
    NegSqDistSimilarity neg_sq_dist_similarity = 1;
    IouSimilarity iou_similarity = 2;
    IoaSimilarity ioa_similarity = 3;
  }
}

// Configuration for negative squared distance similarity calculator.
message NegSqDistSimilarity {
}

// Configuration for intersection-over-union (IOU) similarity calculator.
message IouSimilarity {
}

// Configuration for intersection-over-area (IOA) similarity calculator.
message IoaSimilarity {
}
