  syntax = "proto2";

package object_detection.protos;

// Configuration proto for RetinaNet anchor generator described in
// https://arxiv.org/abs/1708.02002. See
// anchor_generators/multiscale_grid_anchor_generator.py for details.
message MultiscaleAnchorGenerator {
  // minimum level in feature pyramid
  optional int32 min_level = 1 [default = 3];

  // maximum level in feature pyramid
  optional int32 max_level = 2 [default = 7];

  // Scale of anchor to feature stride
  optional float anchor_scale = 3 [default = 4.0];

  // Aspect ratios for anchors at each grid point.
  repeated float aspect_ratios = 4;

  // Number of intermediate scale each scale octave
  optional int32 scales_per_octave = 5 [default = 2];
}
