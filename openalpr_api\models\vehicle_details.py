# coding: utf-8

"""
    OpenALPR Cloud API

    No descripton provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)

    OpenAPI spec version: 2.0.1
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git

    Licensed under the Apache License, Version 2.0 (the "License");
    you may not use this file except in compliance with the License.
    You may obtain a copy of the License at

        http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.
"""

from pprint import pformat
from six import iteritems
import re


class VehicleDetails(object):
    """
    NOTE: This class is auto generated by the swagger code generator program.
    Do not edit the class manually.
    """
    def __init__(self, color=None, make=None, make_model=None, body_type=None):
        """
        VehicleDetails - a model defined in Swagger

        :param dict swaggerTypes: The key is attribute name
                                  and the value is attribute type.
        :param dict attributeMap: The key is attribute name
                                  and the value is json key in definition.
        """
        self.swagger_types = {
            'color': 'list[VehicleCandidate]',
            'make': 'list[VehicleCandidate]',
            'make_model': 'list[VehicleCandidate]',
            'body_type': 'list[VehicleCandidate]'
        }

        self.attribute_map = {
            'color': 'color',
            'make': 'make',
            'make_model': 'make_model',
            'body_type': 'body_type'
        }

        self._color = color
        self._make = make
        self._make_model = make_model
        self._body_type = body_type

    @property
    def color(self):
        """
        Gets the color of this VehicleDetails.


        :return: The color of this VehicleDetails.
        :rtype: list[VehicleCandidate]
        """
        return self._color

    @color.setter
    def color(self, color):
        """
        Sets the color of this VehicleDetails.


        :param color: The color of this VehicleDetails.
        :type: list[VehicleCandidate]
        """

        self._color = color

    @property
    def make(self):
        """
        Gets the make of this VehicleDetails.


        :return: The make of this VehicleDetails.
        :rtype: list[VehicleCandidate]
        """
        return self._make

    @make.setter
    def make(self, make):
        """
        Sets the make of this VehicleDetails.


        :param make: The make of this VehicleDetails.
        :type: list[VehicleCandidate]
        """

        self._make = make

    @property
    def make_model(self):
        """
        Gets the make_model of this VehicleDetails.


        :return: The make_model of this VehicleDetails.
        :rtype: list[VehicleCandidate]
        """
        return self._make_model

    @make_model.setter
    def make_model(self, make_model):
        """
        Sets the make_model of this VehicleDetails.


        :param make_model: The make_model of this VehicleDetails.
        :type: list[VehicleCandidate]
        """

        self._make_model = make_model

    @property
    def body_type(self):
        """
        Gets the body_type of this VehicleDetails.


        :return: The body_type of this VehicleDetails.
        :rtype: list[VehicleCandidate]
        """
        return self._body_type

    @body_type.setter
    def body_type(self, body_type):
        """
        Sets the body_type of this VehicleDetails.


        :param body_type: The body_type of this VehicleDetails.
        :type: list[VehicleCandidate]
        """

        self._body_type = body_type

    def to_dict(self):
        """
        Returns the model properties as a dict
        """
        result = {}

        for attr, _ in iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value

        return result

    def to_str(self):
        """
        Returns the string representation of the model
        """
        return pformat(self.to_dict())

    def __repr__(self):
        """
        For `print` and `pprint`
        """
        return self.to_str()

    def __eq__(self, other):
        """
        Returns true if both objects are equal
        """
        return self.__dict__ == other.__dict__

    def __ne__(self, other):
        """
        Returns true if both objects are not equal
        """
        return not self == other
