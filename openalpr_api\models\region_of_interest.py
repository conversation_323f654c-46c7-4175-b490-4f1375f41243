# coding: utf-8

"""
    OpenALPR Cloud API

    No descripton provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)

    OpenAPI spec version: 2.0.1
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git

    Licensed under the Apache License, Version 2.0 (the "License");
    you may not use this file except in compliance with the License.
    You may obtain a copy of the License at

        http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.
"""

from pprint import pformat
from six import iteritems
import re


class RegionOfInterest(object):
    """
    NOTE: This class is auto generated by the swagger code generator program.
    Do not edit the class manually.
    """
    def __init__(self, x=None, y=None, width=None, height=None):
        """
        RegionOfInterest - a model defined in Swagger

        :param dict swaggerTypes: The key is attribute name
                                  and the value is attribute type.
        :param dict attributeMap: The key is attribute name
                                  and the value is json key in definition.
        """
        self.swagger_types = {
            'x': 'int',
            'y': 'int',
            'width': 'int',
            'height': 'int'
        }

        self.attribute_map = {
            'x': 'x',
            'y': 'y',
            'width': 'width',
            'height': 'height'
        }

        self._x = x
        self._y = y
        self._width = width
        self._height = height

    @property
    def x(self):
        """
        Gets the x of this RegionOfInterest.


        :return: The x of this RegionOfInterest.
        :rtype: int
        """
        return self._x

    @x.setter
    def x(self, x):
        """
        Sets the x of this RegionOfInterest.


        :param x: The x of this RegionOfInterest.
        :type: int
        """

        self._x = x

    @property
    def y(self):
        """
        Gets the y of this RegionOfInterest.


        :return: The y of this RegionOfInterest.
        :rtype: int
        """
        return self._y

    @y.setter
    def y(self, y):
        """
        Sets the y of this RegionOfInterest.


        :param y: The y of this RegionOfInterest.
        :type: int
        """

        self._y = y

    @property
    def width(self):
        """
        Gets the width of this RegionOfInterest.


        :return: The width of this RegionOfInterest.
        :rtype: int
        """
        return self._width

    @width.setter
    def width(self, width):
        """
        Sets the width of this RegionOfInterest.


        :param width: The width of this RegionOfInterest.
        :type: int
        """

        self._width = width

    @property
    def height(self):
        """
        Gets the height of this RegionOfInterest.


        :return: The height of this RegionOfInterest.
        :rtype: int
        """
        return self._height

    @height.setter
    def height(self, height):
        """
        Sets the height of this RegionOfInterest.


        :param height: The height of this RegionOfInterest.
        :type: int
        """

        self._height = height

    def to_dict(self):
        """
        Returns the model properties as a dict
        """
        result = {}

        for attr, _ in iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value

        return result

    def to_str(self):
        """
        Returns the string representation of the model
        """
        return pformat(self.to_dict())

    def __repr__(self):
        """
        For `print` and `pprint`
        """
        return self.to_str()

    def __eq__(self, other):
        """
        Returns true if both objects are equal
        """
        return self.__dict__ == other.__dict__

    def __ne__(self, other):
        """
        Returns true if both objects are not equal
        """
        return not self == other
