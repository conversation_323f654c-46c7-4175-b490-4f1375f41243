# coding: utf-8

"""
    OpenALPR Cloud API

    No descripton provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)

    OpenAPI spec version: 2.0.1
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git

    Licensed under the Apache License, Version 2.0 (the "License");
    you may not use this file except in compliance with the License.
    You may obtain a copy of the License at

        http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.
"""

from __future__ import absolute_import

import sys
import os
import re

# python 2 and python 3 compatibility library
from six import iteritems

from ..configuration import Configuration
from ..api_client import ApiClient


class DefaultApi(object):
    """
    NOTE: This class is auto generated by the swagger code generator program.
    Do not edit the class manually.
    Ref: https://github.com/swagger-api/swagger-codegen
    """

    def __init__(self, api_client=None):
        config = Configuration()
        if api_client:
            self.api_client = api_client
        else:
            if not config.api_client:
                config.api_client = ApiClient()
            self.api_client = config.api_client

    def recognize_bytes(self, image_bytes, secret_key, country, **kwargs):
        """
        
        Send an image for OpenALPR to analyze and provide metadata back The image is sent as base64 encoded bytes. 

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please define a `callback` function
        to be invoked when receiving the response.
        >>> def callback_function(response):
        >>>     pprint(response)
        >>>
        >>> thread = api.recognize_bytes(image_bytes, secret_key, country, callback=callback_function)

        :param callback function: The callback function
            for asynchronous request. (optional)
        :param str image_bytes: The image file that you wish to analyze encoded in base64  (required)
        :param str secret_key: The secret key used to authenticate your account.  You can view your  secret key by visiting  https://cloud.openalpr.com/  (required)
        :param str country: Defines the training data used by OpenALPR.  \"us\" analyzes  North-American style plates.  \"eu\" analyzes European-style plates.  This field is required if using the \"plate\" task  You may use multiple datasets by using commas between the country  codes.  For example, 'au,auwide' would analyze using both the  Australian plate styles.  A full list of supported country codes  can be found here https://github.com/openalpr/openalpr/tree/master/runtime_data/config  (required)
        :param int recognize_vehicle: If set to 1, the vehicle will also be recognized in the image This requires an additional credit per request 
        :param str state: Corresponds to a US state or EU country code used by OpenALPR pattern  recognition.  For example, using \"md\" matches US plates against the  Maryland plate patterns.  Using \"fr\" matches European plates against  the French plate patterns. 
        :param int return_image: If set to 1, the image you uploaded will be encoded in base64 and  sent back along with the response 
        :param int topn: The number of results you would like to be returned for plate  candidates and vehicle classifications 
        :param str prewarp: Prewarp configuration is used to calibrate the analyses for the  angle of a particular camera.  More information is available here http://doc.openalpr.com/accuracy_improvements.html#calibration 
        :return: InlineResponse200
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('callback'):
            return self.recognize_bytes_with_http_info(image_bytes, secret_key, country, **kwargs)
        else:
            (data) = self.recognize_bytes_with_http_info(image_bytes, secret_key, country, **kwargs)
            return data

    def recognize_bytes_with_http_info(self, image_bytes, secret_key, country, **kwargs):
        """
        
        Send an image for OpenALPR to analyze and provide metadata back The image is sent as base64 encoded bytes. 

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please define a `callback` function
        to be invoked when receiving the response.
        >>> def callback_function(response):
        >>>     pprint(response)
        >>>
        >>> thread = api.recognize_bytes_with_http_info(image_bytes, secret_key, country, callback=callback_function)

        :param callback function: The callback function
            for asynchronous request. (optional)
        :param str image_bytes: The image file that you wish to analyze encoded in base64  (required)
        :param str secret_key: The secret key used to authenticate your account.  You can view your  secret key by visiting  https://cloud.openalpr.com/  (required)
        :param str country: Defines the training data used by OpenALPR.  \"us\" analyzes  North-American style plates.  \"eu\" analyzes European-style plates.  This field is required if using the \"plate\" task  You may use multiple datasets by using commas between the country  codes.  For example, 'au,auwide' would analyze using both the  Australian plate styles.  A full list of supported country codes  can be found here https://github.com/openalpr/openalpr/tree/master/runtime_data/config  (required)
        :param int recognize_vehicle: If set to 1, the vehicle will also be recognized in the image This requires an additional credit per request 
        :param str state: Corresponds to a US state or EU country code used by OpenALPR pattern  recognition.  For example, using \"md\" matches US plates against the  Maryland plate patterns.  Using \"fr\" matches European plates against  the French plate patterns. 
        :param int return_image: If set to 1, the image you uploaded will be encoded in base64 and  sent back along with the response 
        :param int topn: The number of results you would like to be returned for plate  candidates and vehicle classifications 
        :param str prewarp: Prewarp configuration is used to calibrate the analyses for the  angle of a particular camera.  More information is available here http://doc.openalpr.com/accuracy_improvements.html#calibration 
        :return: InlineResponse200
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['image_bytes', 'secret_key', 'country', 'recognize_vehicle', 'state', 'return_image', 'topn', 'prewarp']
        all_params.append('callback')
        all_params.append('_return_http_data_only')

        params = locals()
        for key, val in iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method recognize_bytes" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'image_bytes' is set
        if ('image_bytes' not in params) or (params['image_bytes'] is None):
            raise ValueError("Missing the required parameter `image_bytes` when calling `recognize_bytes`")
        # verify the required parameter 'secret_key' is set
        if ('secret_key' not in params) or (params['secret_key'] is None):
            raise ValueError("Missing the required parameter `secret_key` when calling `recognize_bytes`")
        # verify the required parameter 'country' is set
        if ('country' not in params) or (params['country'] is None):
            raise ValueError("Missing the required parameter `country` when calling `recognize_bytes`")

        if 'topn' in params and params['topn'] > 1000.0:
            raise ValueError("Invalid value for parameter `topn` when calling `recognize_bytes`, must be a value less than or equal to  `1000.0`")
        if 'topn' in params and params['topn'] < 1.0:
            raise ValueError("Invalid value for parameter `topn` when calling `recognize_bytes`, must be a value greater than or equal to `1.0`")
        resource_path = '/recognize_bytes'.replace('{format}', 'json')
        path_params = {}

        query_params = {}
        if 'secret_key' in params:
            query_params['secret_key'] = params['secret_key']
        if 'recognize_vehicle' in params:
            query_params['recognize_vehicle'] = params['recognize_vehicle']
        if 'country' in params:
            query_params['country'] = params['country']
        if 'state' in params:
            query_params['state'] = params['state']
        if 'return_image' in params:
            query_params['return_image'] = params['return_image']
        if 'topn' in params:
            query_params['topn'] = params['topn']
        if 'prewarp' in params:
            query_params['prewarp'] = params['prewarp']

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'image_bytes' in params:
            body_params = params['image_bytes']

        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.\
            select_header_accept(['application/json'])
        if not header_params['Accept']:
            del header_params['Accept']

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.\
            select_header_content_type(['application/json'])

        # Authentication setting
        auth_settings = []

        return self.api_client.call_api(resource_path, 'POST',
                                            path_params,
                                            query_params,
                                            header_params,
                                            body=body_params,
                                            post_params=form_params,
                                            files=local_var_files,
                                            response_type='InlineResponse200',
                                            auth_settings=auth_settings,
                                            callback=params.get('callback'),
                                            _return_http_data_only=params.get('_return_http_data_only'))

    def recognize_file(self, image, secret_key, country, **kwargs):
        """
        
        Send an image for OpenALPR to analyze and provide metadata back The image is sent as a file using a form data POST 

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please define a `callback` function
        to be invoked when receiving the response.
        >>> def callback_function(response):
        >>>     pprint(response)
        >>>
        >>> thread = api.recognize_file(image, secret_key, country, callback=callback_function)

        :param callback function: The callback function
            for asynchronous request. (optional)
        :param file image: The image file that you wish to analyze  (required)
        :param str secret_key: The secret key used to authenticate your account.  You can view your  secret key by visiting  https://cloud.openalpr.com/  (required)
        :param str country: Defines the training data used by OpenALPR.  \"us\" analyzes  North-American style plates.  \"eu\" analyzes European-style plates.  This field is required if using the \"plate\" task  You may use multiple datasets by using commas between the country  codes.  For example, 'au,auwide' would analyze using both the  Australian plate styles.  A full list of supported country codes  can be found here https://github.com/openalpr/openalpr/tree/master/runtime_data/config  (required)
        :param int recognize_vehicle: If set to 1, the vehicle will also be recognized in the image This requires an additional credit per request 
        :param str state: Corresponds to a US state or EU country code used by OpenALPR pattern  recognition.  For example, using \"md\" matches US plates against the  Maryland plate patterns.  Using \"fr\" matches European plates against  the French plate patterns. 
        :param int return_image: If set to 1, the image you uploaded will be encoded in base64 and  sent back along with the response 
        :param int topn: The number of results you would like to be returned for plate  candidates and vehicle classifications 
        :param str prewarp: Prewarp configuration is used to calibrate the analyses for the  angle of a particular camera.  More information is available here http://doc.openalpr.com/accuracy_improvements.html#calibration 
        :return: InlineResponse200
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('callback'):
            return self.recognize_file_with_http_info(image, secret_key, country, **kwargs)
        else:
            (data) = self.recognize_file_with_http_info(image, secret_key, country, **kwargs)
            return data

    def recognize_file_with_http_info(self, image, secret_key, country, **kwargs):
        """
        
        Send an image for OpenALPR to analyze and provide metadata back The image is sent as a file using a form data POST 

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please define a `callback` function
        to be invoked when receiving the response.
        >>> def callback_function(response):
        >>>     pprint(response)
        >>>
        >>> thread = api.recognize_file_with_http_info(image, secret_key, country, callback=callback_function)

        :param callback function: The callback function
            for asynchronous request. (optional)
        :param file image: The image file that you wish to analyze  (required)
        :param str secret_key: The secret key used to authenticate your account.  You can view your  secret key by visiting  https://cloud.openalpr.com/  (required)
        :param str country: Defines the training data used by OpenALPR.  \"us\" analyzes  North-American style plates.  \"eu\" analyzes European-style plates.  This field is required if using the \"plate\" task  You may use multiple datasets by using commas between the country  codes.  For example, 'au,auwide' would analyze using both the  Australian plate styles.  A full list of supported country codes  can be found here https://github.com/openalpr/openalpr/tree/master/runtime_data/config  (required)
        :param int recognize_vehicle: If set to 1, the vehicle will also be recognized in the image This requires an additional credit per request 
        :param str state: Corresponds to a US state or EU country code used by OpenALPR pattern  recognition.  For example, using \"md\" matches US plates against the  Maryland plate patterns.  Using \"fr\" matches European plates against  the French plate patterns. 
        :param int return_image: If set to 1, the image you uploaded will be encoded in base64 and  sent back along with the response 
        :param int topn: The number of results you would like to be returned for plate  candidates and vehicle classifications 
        :param str prewarp: Prewarp configuration is used to calibrate the analyses for the  angle of a particular camera.  More information is available here http://doc.openalpr.com/accuracy_improvements.html#calibration 
        :return: InlineResponse200
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['image', 'secret_key', 'country', 'recognize_vehicle', 'state', 'return_image', 'topn', 'prewarp']
        all_params.append('callback')
        all_params.append('_return_http_data_only')

        params = locals()
        for key, val in iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method recognize_file" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'image' is set
        if ('image' not in params) or (params['image'] is None):
            raise ValueError("Missing the required parameter `image` when calling `recognize_file`")
        # verify the required parameter 'secret_key' is set
        if ('secret_key' not in params) or (params['secret_key'] is None):
            raise ValueError("Missing the required parameter `secret_key` when calling `recognize_file`")
        # verify the required parameter 'country' is set
        if ('country' not in params) or (params['country'] is None):
            raise ValueError("Missing the required parameter `country` when calling `recognize_file`")

        if 'topn' in params and params['topn'] > 1000.0:
            raise ValueError("Invalid value for parameter `topn` when calling `recognize_file`, must be a value less than or equal to  `1000.0`")
        if 'topn' in params and params['topn'] < 1.0:
            raise ValueError("Invalid value for parameter `topn` when calling `recognize_file`, must be a value greater than or equal to `1.0`")
        resource_path = '/recognize'.replace('{format}', 'json')
        path_params = {}

        query_params = {}
        if 'secret_key' in params:
            query_params['secret_key'] = params['secret_key']
        if 'recognize_vehicle' in params:
            query_params['recognize_vehicle'] = params['recognize_vehicle']
        if 'country' in params:
            query_params['country'] = params['country']
        if 'state' in params:
            query_params['state'] = params['state']
        if 'return_image' in params:
            query_params['return_image'] = params['return_image']
        if 'topn' in params:
            query_params['topn'] = params['topn']
        if 'prewarp' in params:
            query_params['prewarp'] = params['prewarp']

        header_params = {}

        form_params = []
        local_var_files = {}
        if 'image' in params:
            local_var_files['image'] = params['image']

        body_params = None

        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.\
            select_header_accept(['application/json'])
        if not header_params['Accept']:
            del header_params['Accept']

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.\
            select_header_content_type(['multipart/form-data'])

        # Authentication setting
        auth_settings = []

        return self.api_client.call_api(resource_path, 'POST',
                                            path_params,
                                            query_params,
                                            header_params,
                                            body=body_params,
                                            post_params=form_params,
                                            files=local_var_files,
                                            response_type='InlineResponse200',
                                            auth_settings=auth_settings,
                                            callback=params.get('callback'),
                                            _return_http_data_only=params.get('_return_http_data_only'))

    def recognize_url(self, image_url, secret_key, country, **kwargs):
        """
        
        Send an image for OpenALPR to analyze and provide metadata back The image is sent as a URL.  The OpenALPR service will download the image  and process it 

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please define a `callback` function
        to be invoked when receiving the response.
        >>> def callback_function(response):
        >>>     pprint(response)
        >>>
        >>> thread = api.recognize_url(image_url, secret_key, country, callback=callback_function)

        :param callback function: The callback function
            for asynchronous request. (optional)
        :param str image_url: A URL to an image that you wish to analyze  (required)
        :param str secret_key: The secret key used to authenticate your account.  You can view your  secret key by visiting  https://cloud.openalpr.com/  (required)
        :param str country: Defines the training data used by OpenALPR.  \"us\" analyzes  North-American style plates.  \"eu\" analyzes European-style plates.  This field is required if using the \"plate\" task  You may use multiple datasets by using commas between the country  codes.  For example, 'au,auwide' would analyze using both the  Australian plate styles.  A full list of supported country codes  can be found here https://github.com/openalpr/openalpr/tree/master/runtime_data/config  (required)
        :param int recognize_vehicle: If set to 1, the vehicle will also be recognized in the image This requires an additional credit per request 
        :param str state: Corresponds to a US state or EU country code used by OpenALPR pattern  recognition.  For example, using \"md\" matches US plates against the  Maryland plate patterns.  Using \"fr\" matches European plates against  the French plate patterns. 
        :param int return_image: If set to 1, the image you uploaded will be encoded in base64 and  sent back along with the response 
        :param int topn: The number of results you would like to be returned for plate  candidates and vehicle classifications 
        :param str prewarp: Prewarp configuration is used to calibrate the analyses for the  angle of a particular camera.  More information is available here http://doc.openalpr.com/accuracy_improvements.html#calibration 
        :return: InlineResponse200
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('callback'):
            return self.recognize_url_with_http_info(image_url, secret_key, country, **kwargs)
        else:
            (data) = self.recognize_url_with_http_info(image_url, secret_key, country, **kwargs)
            return data

    def recognize_url_with_http_info(self, image_url, secret_key, country, **kwargs):
        """
        
        Send an image for OpenALPR to analyze and provide metadata back The image is sent as a URL.  The OpenALPR service will download the image  and process it 

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please define a `callback` function
        to be invoked when receiving the response.
        >>> def callback_function(response):
        >>>     pprint(response)
        >>>
        >>> thread = api.recognize_url_with_http_info(image_url, secret_key, country, callback=callback_function)

        :param callback function: The callback function
            for asynchronous request. (optional)
        :param str image_url: A URL to an image that you wish to analyze  (required)
        :param str secret_key: The secret key used to authenticate your account.  You can view your  secret key by visiting  https://cloud.openalpr.com/  (required)
        :param str country: Defines the training data used by OpenALPR.  \"us\" analyzes  North-American style plates.  \"eu\" analyzes European-style plates.  This field is required if using the \"plate\" task  You may use multiple datasets by using commas between the country  codes.  For example, 'au,auwide' would analyze using both the  Australian plate styles.  A full list of supported country codes  can be found here https://github.com/openalpr/openalpr/tree/master/runtime_data/config  (required)
        :param int recognize_vehicle: If set to 1, the vehicle will also be recognized in the image This requires an additional credit per request 
        :param str state: Corresponds to a US state or EU country code used by OpenALPR pattern  recognition.  For example, using \"md\" matches US plates against the  Maryland plate patterns.  Using \"fr\" matches European plates against  the French plate patterns. 
        :param int return_image: If set to 1, the image you uploaded will be encoded in base64 and  sent back along with the response 
        :param int topn: The number of results you would like to be returned for plate  candidates and vehicle classifications 
        :param str prewarp: Prewarp configuration is used to calibrate the analyses for the  angle of a particular camera.  More information is available here http://doc.openalpr.com/accuracy_improvements.html#calibration 
        :return: InlineResponse200
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['image_url', 'secret_key', 'country', 'recognize_vehicle', 'state', 'return_image', 'topn', 'prewarp']
        all_params.append('callback')
        all_params.append('_return_http_data_only')

        params = locals()
        for key, val in iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method recognize_url" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'image_url' is set
        if ('image_url' not in params) or (params['image_url'] is None):
            raise ValueError("Missing the required parameter `image_url` when calling `recognize_url`")
        # verify the required parameter 'secret_key' is set
        if ('secret_key' not in params) or (params['secret_key'] is None):
            raise ValueError("Missing the required parameter `secret_key` when calling `recognize_url`")
        # verify the required parameter 'country' is set
        if ('country' not in params) or (params['country'] is None):
            raise ValueError("Missing the required parameter `country` when calling `recognize_url`")

        if 'topn' in params and params['topn'] > 1000.0:
            raise ValueError("Invalid value for parameter `topn` when calling `recognize_url`, must be a value less than or equal to  `1000.0`")
        if 'topn' in params and params['topn'] < 1.0:
            raise ValueError("Invalid value for parameter `topn` when calling `recognize_url`, must be a value greater than or equal to `1.0`")
        resource_path = '/recognize_url'.replace('{format}', 'json')
        path_params = {}

        query_params = {}
        if 'image_url' in params:
            query_params['image_url'] = params['image_url']
        if 'secret_key' in params:
            query_params['secret_key'] = params['secret_key']
        if 'recognize_vehicle' in params:
            query_params['recognize_vehicle'] = params['recognize_vehicle']
        if 'country' in params:
            query_params['country'] = params['country']
        if 'state' in params:
            query_params['state'] = params['state']
        if 'return_image' in params:
            query_params['return_image'] = params['return_image']
        if 'topn' in params:
            query_params['topn'] = params['topn']
        if 'prewarp' in params:
            query_params['prewarp'] = params['prewarp']

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None

        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.\
            select_header_accept(['application/json'])
        if not header_params['Accept']:
            del header_params['Accept']

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.\
            select_header_content_type(['application/json'])

        # Authentication setting
        auth_settings = []

        return self.api_client.call_api(resource_path, 'POST',
                                            path_params,
                                            query_params,
                                            header_params,
                                            body=body_params,
                                            post_params=form_params,
                                            files=local_var_files,
                                            response_type='InlineResponse200',
                                            auth_settings=auth_settings,
                                            callback=params.get('callback'),
                                            _return_http_data_only=params.get('_return_http_data_only'))
