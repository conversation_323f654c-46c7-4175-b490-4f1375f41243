syntax = "proto2";

package object_detection.protos;

import "object_detection/protos/faster_rcnn_box_coder.proto";
import "object_detection/protos/keypoint_box_coder.proto";
import "object_detection/protos/mean_stddev_box_coder.proto";
import "object_detection/protos/square_box_coder.proto";

// Configuration proto for the box coder to be used in the object detection
// pipeline. See core/box_coder.py for details.
message BoxCoder {
  oneof box_coder_oneof {
    FasterRcnnBoxCoder faster_rcnn_box_coder = 1;
    MeanStddevBoxCoder mean_stddev_box_coder = 2;
    SquareBoxCoder square_box_coder = 3;
    KeypointBoxCoder keypoint_box_coder = 4;
  }
}
