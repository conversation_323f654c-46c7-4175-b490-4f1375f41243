syntax = "proto2";

package object_detection.protos;

// Configuration proto for KeypointBoxCoder. See
// box_coders/keypoint_box_coder.py for details.
message KeypointBoxCoder {
  optional int32 num_keypoints = 1;

  // Scale factor for anchor encoded box center and keypoints.
  optional float y_scale = 2 [default = 10.0];
  optional float x_scale = 3 [default = 10.0];

  // Scale factor for anchor encoded box height.
  optional float height_scale = 4 [default = 5.0];

  // Scale factor for anchor encoded box width.
  optional float width_scale = 5 [default = 5.0];
}
