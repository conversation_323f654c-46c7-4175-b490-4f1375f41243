# coding: utf-8

"""
    OpenALPR Cloud API

    No descripton provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)

    OpenAPI spec version: 2.0.1
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git

    Licensed under the Apache License, Version 2.0 (the "License");
    you may not use this file except in compliance with the License.
    You may obtain a copy of the License at

        http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.
"""

from pprint import pformat
from six import iteritems
import re


class InlineResponse200(object):
    """
    NOTE: This class is auto generated by the swagger code generator program.
    Do not edit the class manually.
    """
    def __init__(self, processing_time=None, img_width=None, img_height=None, credit_cost=None, credits_monthly_used=None, credits_monthly_total=None, results=None, regions_of_interest=None, epoch_time=None, version=None, data_type=None):
        """
        InlineResponse200 - a model defined in Swagger

        :param dict swaggerTypes: The key is attribute name
                                  and the value is attribute type.
        :param dict attributeMap: The key is attribute name
                                  and the value is json key in definition.
        """
        self.swagger_types = {
            'processing_time': 'InlineResponse200ProcessingTime',
            'img_width': 'int',
            'img_height': 'int',
            'credit_cost': 'int',
            'credits_monthly_used': 'int',
            'credits_monthly_total': 'int',
            'results': 'list[PlateDetails]',
            'regions_of_interest': 'list[RegionOfInterest]',
            'epoch_time': 'float',
            'version': 'int',
            'data_type': 'str'
        }

        self.attribute_map = {
            'processing_time': 'processing_time',
            'img_width': 'img_width',
            'img_height': 'img_height',
            'credit_cost': 'credit_cost',
            'credits_monthly_used': 'credits_monthly_used',
            'credits_monthly_total': 'credits_monthly_total',
            'results': 'results',
            'regions_of_interest': 'regions_of_interest',
            'epoch_time': 'epoch_time',
            'version': 'version',
            'data_type': 'data_type'
        }

        self._processing_time = processing_time
        self._img_width = img_width
        self._img_height = img_height
        self._credit_cost = credit_cost
        self._credits_monthly_used = credits_monthly_used
        self._credits_monthly_total = credits_monthly_total
        self._results = results
        self._regions_of_interest = regions_of_interest
        self._epoch_time = epoch_time
        self._version = version
        self._data_type = data_type

    @property
    def processing_time(self):
        """
        Gets the processing_time of this InlineResponse200.


        :return: The processing_time of this InlineResponse200.
        :rtype: InlineResponse200ProcessingTime
        """
        return self._processing_time

    @processing_time.setter
    def processing_time(self, processing_time):
        """
        Sets the processing_time of this InlineResponse200.


        :param processing_time: The processing_time of this InlineResponse200.
        :type: InlineResponse200ProcessingTime
        """

        self._processing_time = processing_time

    @property
    def img_width(self):
        """
        Gets the img_width of this InlineResponse200.
        Width of the uploaded image in pixels

        :return: The img_width of this InlineResponse200.
        :rtype: int
        """
        return self._img_width

    @img_width.setter
    def img_width(self, img_width):
        """
        Sets the img_width of this InlineResponse200.
        Width of the uploaded image in pixels

        :param img_width: The img_width of this InlineResponse200.
        :type: int
        """

        self._img_width = img_width

    @property
    def img_height(self):
        """
        Gets the img_height of this InlineResponse200.
        Height of the input image in pixels

        :return: The img_height of this InlineResponse200.
        :rtype: int
        """
        return self._img_height

    @img_height.setter
    def img_height(self, img_height):
        """
        Sets the img_height of this InlineResponse200.
        Height of the input image in pixels

        :param img_height: The img_height of this InlineResponse200.
        :type: int
        """

        self._img_height = img_height

    @property
    def credit_cost(self):
        """
        Gets the credit_cost of this InlineResponse200.
        The number of API credits that were used to process this image

        :return: The credit_cost of this InlineResponse200.
        :rtype: int
        """
        return self._credit_cost

    @credit_cost.setter
    def credit_cost(self, credit_cost):
        """
        Sets the credit_cost of this InlineResponse200.
        The number of API credits that were used to process this image

        :param credit_cost: The credit_cost of this InlineResponse200.
        :type: int
        """

        self._credit_cost = credit_cost

    @property
    def credits_monthly_used(self):
        """
        Gets the credits_monthly_used of this InlineResponse200.
        The number of API credits used this month

        :return: The credits_monthly_used of this InlineResponse200.
        :rtype: int
        """
        return self._credits_monthly_used

    @credits_monthly_used.setter
    def credits_monthly_used(self, credits_monthly_used):
        """
        Sets the credits_monthly_used of this InlineResponse200.
        The number of API credits used this month

        :param credits_monthly_used: The credits_monthly_used of this InlineResponse200.
        :type: int
        """

        self._credits_monthly_used = credits_monthly_used

    @property
    def credits_monthly_total(self):
        """
        Gets the credits_monthly_total of this InlineResponse200.
        The maximum number of API credits available this month according to your plan

        :return: The credits_monthly_total of this InlineResponse200.
        :rtype: int
        """
        return self._credits_monthly_total

    @credits_monthly_total.setter
    def credits_monthly_total(self, credits_monthly_total):
        """
        Sets the credits_monthly_total of this InlineResponse200.
        The maximum number of API credits available this month according to your plan

        :param credits_monthly_total: The credits_monthly_total of this InlineResponse200.
        :type: int
        """

        self._credits_monthly_total = credits_monthly_total

    @property
    def results(self):
        """
        Gets the results of this InlineResponse200.


        :return: The results of this InlineResponse200.
        :rtype: list[PlateDetails]
        """
        return self._results

    @results.setter
    def results(self, results):
        """
        Sets the results of this InlineResponse200.


        :param results: The results of this InlineResponse200.
        :type: list[PlateDetails]
        """

        self._results = results

    @property
    def regions_of_interest(self):
        """
        Gets the regions_of_interest of this InlineResponse200.
        Describes the areas analyzed in the input image

        :return: The regions_of_interest of this InlineResponse200.
        :rtype: list[RegionOfInterest]
        """
        return self._regions_of_interest

    @regions_of_interest.setter
    def regions_of_interest(self, regions_of_interest):
        """
        Sets the regions_of_interest of this InlineResponse200.
        Describes the areas analyzed in the input image

        :param regions_of_interest: The regions_of_interest of this InlineResponse200.
        :type: list[RegionOfInterest]
        """

        self._regions_of_interest = regions_of_interest

    @property
    def epoch_time(self):
        """
        Gets the epoch_time of this InlineResponse200.
        Epoch time that the image was processed in milliseconds

        :return: The epoch_time of this InlineResponse200.
        :rtype: float
        """
        return self._epoch_time

    @epoch_time.setter
    def epoch_time(self, epoch_time):
        """
        Sets the epoch_time of this InlineResponse200.
        Epoch time that the image was processed in milliseconds

        :param epoch_time: The epoch_time of this InlineResponse200.
        :type: float
        """

        self._epoch_time = epoch_time

    @property
    def version(self):
        """
        Gets the version of this InlineResponse200.
        API format version

        :return: The version of this InlineResponse200.
        :rtype: int
        """
        return self._version

    @version.setter
    def version(self, version):
        """
        Sets the version of this InlineResponse200.
        API format version

        :param version: The version of this InlineResponse200.
        :type: int
        """

        self._version = version

    @property
    def data_type(self):
        """
        Gets the data_type of this InlineResponse200.
        Specifies the type of data in this response

        :return: The data_type of this InlineResponse200.
        :rtype: str
        """
        return self._data_type

    @data_type.setter
    def data_type(self, data_type):
        """
        Sets the data_type of this InlineResponse200.
        Specifies the type of data in this response

        :param data_type: The data_type of this InlineResponse200.
        :type: str
        """
        allowed_values = ["alpr_results", "alpr_group", "heartbeat"]
        if data_type not in allowed_values:
            raise ValueError(
                "Invalid value for `data_type` ({0}), must be one of {1}"
                .format(data_type, allowed_values)
            )

        self._data_type = data_type

    def to_dict(self):
        """
        Returns the model properties as a dict
        """
        result = {}

        for attr, _ in iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value

        return result

    def to_str(self):
        """
        Returns the string representation of the model
        """
        return pformat(self.to_dict())

    def __repr__(self):
        """
        For `print` and `pprint`
        """
        return self.to_str()

    def __eq__(self, other):
        """
        Returns true if both objects are equal
        """
        return self.__dict__ == other.__dict__

    def __ne__(self, other):
        """
        Returns true if both objects are not equal
        """
        return not self == other
