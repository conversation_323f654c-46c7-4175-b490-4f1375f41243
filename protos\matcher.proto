syntax = "proto2";

package object_detection.protos;

import "object_detection/protos/argmax_matcher.proto";
import "object_detection/protos/bipartite_matcher.proto";

// Configuration proto for the matcher to be used in the object detection
// pipeline. See core/matcher.py for details.
message Matcher {
  oneof matcher_oneof {
    ArgMaxMatcher argmax_matcher = 1;
    BipartiteMatcher bipartite_matcher = 2;
  }
}
