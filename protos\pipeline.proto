syntax = "proto2";

package object_detection.protos;

import "object_detection/protos/eval.proto";
import "object_detection/protos/input_reader.proto";
import "object_detection/protos/model.proto";
import "object_detection/protos/train.proto";

// Convenience message for configuring a training and eval pipeline. Allows all
// of the pipeline parameters to be configured from one file.
message TrainEvalPipelineConfig {
  optional DetectionModel model = 1;
  optional TrainConfig train_config = 2;
  optional InputReader train_input_reader = 3;
  optional EvalConfig eval_config = 4;
  optional InputReader eval_input_reader = 5;
  extensions 1000 to max;
}
